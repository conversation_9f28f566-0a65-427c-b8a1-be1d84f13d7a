<template>
  <UCard
    class="bg-neutral-50 dark:bg-neutral-800 hover:opacity-80 cursor-pointer"
    :ui="{
      body: '!py-3'
    }"
    @click="showConfirmModal = true"
  >
    <div class="flex flex-col gap-2">
      <div
        class="text-base font-semibold text-center flex items-center justify-center gap-2"
      >
        <UIcon
          v-if="!isLoading"
          name="i-lucide-bitcoin"
          class="size-6 text-amber-500"
        />
        <UIcon
          v-else
          name="i-lucide-loader-2"
          class="size-6 text-amber-500 animate-spin"
        />
        {{ $t("payWithCrypto") }}
        <div>
          <UBadge
            color="warning"
            variant="subtle"
            class="rounded-full"
            size="sm"
          >
            {{ "+5% credit" }}
          </UBadge>
        </div>
      </div>
      <div class="text-sm text-center text-neutral-600 dark:text-neutral-400">
        {{ $t("cryptoDescription") }}
      </div>
    </div>
  </UCard>

  <!-- Confirm Modal -->
  <UModal v-model:open="showConfirmModal">
    <template #content>
      <UCard
        :ui="{
          ring: '',
          divide: 'divide-y divide-gray-100 dark:divide-gray-800'
        }"
      >
        <template #header>
          <div class="flex items-center justify-between">
            <h3
              class="text-base font-semibold leading-6 text-gray-900 dark:text-white"
            >
              {{ $t("confirmCryptoPayment") }}
            </h3>
            <UButton
              color="neutral"
              variant="ghost"
              icon="i-heroicons-x-mark-20-solid"
              class="-my-1"
              @click="showConfirmModal = false"
            />
          </div>
        </template>

        <div class="space-y-4">
          <div class="flex items-start gap-3">
            <UIcon
              name="i-heroicons-exclamation-triangle"
              class="size-5 text-amber-500 mt-0.5 flex-shrink-0"
            />
            <div class="space-y-2">
              <p class="text-sm text-gray-700 dark:text-gray-300">
                {{ $t("cryptoPaymentNote1", { price: price }) }}
              </p>
              <p class="text-sm text-gray-700 dark:text-gray-300">
                {{ $t("cryptoPaymentNote2") }}
              </p>
            </div>
          </div>
        </div>

        <template #footer>
          <div class="flex justify-end gap-3">
            <UButton
              color="neutral"
              variant="ghost"
              @click="showConfirmModal = false"
            >
              {{ $t("cancel") }}
            </UButton>
            <UButton
              color="primary"
              :loading="isLoading"
              @click="handleCryptoPayment"
            >
              {{ $t("proceedToPayment") }}
            </UButton>
          </div>
        </template>
      </UCard>
    </template>
  </UModal>
</template>

<script setup lang="ts">
const creditsStore = useCreditsStore()
const { isLoading } = storeToRefs(creditsStore)

const showConfirmModal = ref(false)

const handleCryptoPayment = async () => {
  try {
    await creditsStore.createCryptoOrder()
    showConfirmModal.value = false
  } catch (error) {
    // Error handling is done in the store
    console.error('Crypto payment error:', error)
  }
}

defineProps<{
  price: number | string
}>()
</script>
